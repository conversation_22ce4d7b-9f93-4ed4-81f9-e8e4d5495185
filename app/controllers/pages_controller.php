<?php

class PagesController extends AppController {

  /**
   * /testme - debug route to check controller reachability
   */
  function mmenu() {
    // file_put_contents(dirname(__FILE__).'/../tmp/logs/mmenu_debug.txt', 'mmenu hit: '.date('c').PHP_EOL, FILE_APPEND);
    $this->autoRender = false;
    $this->layout = false;

    // if (!property_exists($this, 'Navigation')) {
    //     die('Navigation property does not exist');
    // } else {
    //     die(var_dump($this->components)); // Should show 'Navigation');
    // }

    // // Debug: check Navigation component
    // if (!isset($this->Navigation)) {
    //     die('layout OK');
    //     die('Navigation component not loaded');
    // }

    // $navigationData = $this->Navigation->getNavigationData();
    // if (!$navigationData) {
    //     die('getNavigationData failed');
    // }
    // $this->autoRender = false;
    // $this->layout = false;
    // file_put_contents(dirname(__FILE__).'/../tmp/logs/mmenu_debug.txt', 'before navigationData'.PHP_EOL, FILE_APPEND);
    // Get navigation data from Navigation component
    $navigationData = $this->Navigation->getNavigationData();
    // file_put_contents(dirname(__FILE__).'/../tmp/logs/mmenu_debug.txt', 'after navigationData'.PHP_EOL, FILE_APPEND);
    $mainNav = $navigationData['mainNav'];
    $usaDestinations = $navigationData['usaDestinations'];
    $canadaDestinations = $navigationData['canadaDestinations'];
    $holidayTypes = $navigationData['holidayTypes'];
    $whatsHot = $navigationData['whatsHot'];
    $holidayInfoPages = $navigationData['holidayInfoPages'];
    $aboutPages = $navigationData['aboutPages'];

    // Build mobile navigation structure
    $mobileNavigation = array();
    if (!empty($mainNav['usa']['items'])) {
      $mobileNavigation[] = array(
        'text' => 'USA',
        'url' => '/destinations/usa_holidays',
        'has_children' => true,
        'children' => $this->_mapUsaCanadaDestinations($mainNav['usa']['items'])
      );
    }
    if (!empty($mainNav['canada']['items'])) {
      $mobileNavigation[] = array(
        'text' => 'Canada',
        'url' => '/destinations/canada_holidays',
        'has_children' => true,
        'children' => $this->_mapUsaCanadaDestinations($mainNav['canada']['items'])
      );
    }
    if (!empty($holidayTypes)) {
      $mobileNavigation[] = array(
        'text' => 'Holiday Types',
        'url' => '/holidays',
        'has_children' => true,
        'children' => $this->_mapHolidayTypes($holidayTypes)
      );
    }
    if (!empty($whatsHot)) {
      $mobileNavigation[] = array(
        'text' => "What's Hot",
        'url' => '/spotlights',
        'has_children' => true,
        'children' => $this->_mapWhatsHot($whatsHot)
      );
    }
    if (!empty($holidayInfoPages)) {
      $mobileNavigation[] = array(
        'text' => 'Holiday Info',
        'url' => '/page/holiday_information',
        'has_children' => true,
        'children' => $this->_mapPages($holidayInfoPages)
      );
    }
    if (!empty($aboutPages)) {
      $mobileNavigation[] = array(
        'text' => 'About Us',
        'url' => '/page/about_bon_voyage',
        'has_children' => true,
        'children' => $this->_mapPages($aboutPages)
      );
    }
    $mobileNavigation[] = array('text' => 'Blog', 'url' => '/blog');
    $mobileNavigation[] = array('text' => 'FAQs', 'url' => '/faqs');
    $mobileNavigation[] = array('text' => 'Make an Enquiry', 'url' => '/make_an_enquiry');

    $this->set(compact('mobileNavigation'));
    $this->render('/elements/chrome/mmenu_api');
    // exit;

  }

  function megamenu() {
    $this->autoRender = false;
    $this->layout = 'ajax';

    // Get navigation data from Navigation component
    $navigationData = $this->Navigation->getNavigationData();

    if (!$navigationData) {
      // Log error or handle missing navigation data
      return;
    }

    // Extract navigation components
    $mainNav = $navigationData['mainNav'];
    $usaDestinations = $navigationData['usaDestinations'];
    $canadaDestinations = $navigationData['canadaDestinations'];

    // Get holiday types in the same format as mmenu
    $holidayTypes = !empty($navigationData['holidayTypes']) ? $this->_mapHolidayTypes($navigationData['holidayTypes']) : array();

    $whatsHot = $navigationData['whatsHot'];
    $holidayInfoPages = $navigationData['holidayInfoPages'];
    $aboutPages = $navigationData['aboutPages'];

    // Set variables for the view
    $this->set(compact('mainNav', 'usaDestinations', 'canadaDestinations', 'holidayTypes', 'whatsHot', 'holidayInfoPages', 'aboutPages'));

    // Render the megamenu element
    $this->render('/elements/chrome/mega_menu_api');
  }


  var $name = 'Pages';
  var $components = array('Auth', 'Navigation');
  var $uses = array('Page', 'Testimonial');

  function beforeFilter() {
    parent::beforeFilter();
    // Allow 'hello' action to be accessed without authentication
    if ($this->Auth->allowedActions <> array('*')) {
      $this->Auth->allowedActions = array_merge($this->Auth->allowedActions, array('home', 'hello', 'testme', 'mmenu', 'megamenu'));
    }

        if (Configure::read('Routing.admin')
    && isset($this->params[Configure::read('Routing.admin')])) {

        }
  }

  function view() {
    $this->criticalCss = 'page';

    if (!$slug = $this->params['page_slug']) {
      $this->redirect(array('action' => 'home'));
    }

    $page = $this->_getPage($slug);

        if ($page) {
            if ($slug == 'getting_here') {

          $Contact = ClassRegistry::init('Contact');

          $navigation = $Contact->navigation;

          unset($navigation[0]['selected']);

          $navigation[0]['children'][0]['selected'] = true;

        } else {

          $navigation = $this->_getNavigation($page['Page']['id']);

        }

      $breadcrumbs = $this->_pathToSelected($navigation);

        $this->_setMeta($page['Page']);

      $section = 'pages';

        // Debug: Check if Navigation component is available
        if (!isset($this->Navigation)) {
            error_log('[PagesController::view] Navigation component not available');
        } else {
            error_log('[PagesController::view] Navigation component is available');
        }

        $this->set(compact('page', 'navigation', 'section', 'breadcrumbs'));

        } else {
            $this->redirect(array('action' => 'home'));
        }
  }

  function home() {
    $this->criticalCss = 'home';

    $testimonials = $this->Testimonial->find('all', array(
      'limit' => 10,
      'order' => array('Testimonial.date DESC'),
      'contain' => array(
        'ContentBlock' => array(
          'order' => 'ContentBlock.order ASC',
          'limit' => 1
        )
      ),
      'conditions' => array(
        'Testimonial.published' => 1
      )
    ));

    $itineraries = $this->_randomItineraries();

    $activities = $this->_randomActivities();

    $spotlights = $this->_randomSpotlights();

    $holidays = $this->_randomHolidays();

    $blogPost = $this->_blogPost();

    $page = $this->_getHome();

    $contentBlocks = $page['ContentBlock'];

    $google_tracking = $page['Page']['google_tracking'];

    $heroCarousel = true;
    $hideBreadcrumb = true;

    $this->set(compact('activities', 'itineraries', 'spotlights', 'holidays', 'contentBlocks', 'google_tracking', 'heroCarousel', 'hideBreadcrumb', 'blogPost', 'testimonials'));

    $this->_setMeta($page['Page']);

    $this->set('navDebug', array(
        'controller' => $this->name,
        'parentComponents' => is_array($this->components) ? array_values($this->components) : 'none',
        'components' => array_keys($this->components),
        'hasNavigation' => isset($this->Navigation),
        'navigationClass' => isset($this->Navigation) ? get_class($this->Navigation) : 'not loaded'
    ));

    // Get navigation data once and pass to view
    $navData = $this->Navigation->getNavigationData();
    // error_log("Navigation Data from controller: " . print_r($navData, true));
    // error_log("Holiday Info Pages: " . print_r($navData['holidayInfoPages'], true));
    // error_log("About Pages: " . print_r($navData['aboutPages'], true));

    $this->set(array(
        'usaDestinations' => $navData['usaDestinations'],
        'canadaDestinations' => $navData['canadaDestinations'],
        'holidayTypes' => $navData['holidayTypes'],
        'holidayInfoPages' => $navData['holidayInfoPages'],
        'aboutPages' => $navData['aboutPages']
    ));

    // Transform USA destinations to match model naming convention
    $usaDestinations = array_map(function($dest) {
        return array(
            'Destination' => $dest['destinations']  // Change 'destinations' to 'Destination'
        );
    }, $usaDestinations);

    // Transform Canada destinations
    $canadaDestinations = array_map(function($dest) {
        return array(
            'Destination' => $dest['destinations']  // Change 'destinations' to 'Destination'
        );
    }, $canadaDestinations);

    // Transform holiday types
    $holidayTypes = array_map(function($type) {
        return array(
            'HolidayType' => $type['holiday_types']  // Change 'holiday_types' to 'HolidayType'
        );
    }, $holidayTypes);
  }

  /**
   * Return the page for the given url slug
   *
   * @return array
   **/
  protected function _getPage($slug) {

    $getPage = function() use ($slug) {

      return $this->Page->getBySlug($slug);

    };

    return $this->cacher(implode('_', array(
      'page', $slug, 'content'
    )), $getPage);
  }

  /**
   * Return the navigation for a given page
   *
   * @return array
   **/
  protected function _getNavigation($id) {

    $getNavigation = function() use ($id) {

      return $this->Page->getNavigation($id);

    };

    return $this->cacher(implode('_', array(
      'page', $id, 'navigation'
    )), $getNavigation);
  }

  /**
   * Return the home page content
   **/
  protected function _getHome() {

    $getPage = function() {

      $dbName = ConnectionManager::getDataSource($this->Page->useDbConfig)->config['database'];

      $this->Page->id = Configure::read('PageIds.home.'.$dbName);

      return $this->Page->find('first', array('contain' => array('ContentBlock' => 'Image')));
    };

    return $this->cacher('page_home_content', $getPage);
  }

  /**
   * Return three random itineraries - note these are cached so they're
   * only random on first request or when the cache expires
   */
  protected function _randomItineraries() {

    $randomItineraries = function() {
      return ClassRegistry::init('DestinationsItinerary')->find('randomFeatured', array(
        'conditions' => array(
          'DestinationsItinerary.destination_id >' => 0,
          'DestinationsItinerary.itinerary_id >' => 0
        ),
        'contain' => array(
          'Itinerary' => 'Image',
          'Destination',
        ),
        'limit' => 3
      ));
    };

    return $this->cacher('home_itineraries', $randomItineraries);
  }

  /**
   * Return three random activities - note these are cached so they're
   * only random on first request or when the cache expires
   */
  protected function _randomActivities() {

    $randomDIs = function() {
      return ClassRegistry::init('ActivitiesDestination')->find('randomFeatured', array(
        'conditions' => array(
          'ActivitiesDestination.destination_id >' => 0,
          'ActivitiesDestination.activity_id >' => 0
        ),
        'contain' => array(
          'Activity' => 'Image',
          'Destination',
        ),
        'limit' => 3
      ));
    };

    return $this->cacher('home_activities', $randomDIs);
  }

  /**
   * Return three random spotlights - note these are cached so they're
   * only random on first request or when the cache expires
   */
  protected function _randomSpotlights() {

    $randomSpotlights = function() {
      return ClassRegistry::init('Spotlight')->find('random', array(
        'contain' => array(
          'Image',
        ),
        'limit' => 3
      ));
    };

    return $this->cacher('home_spotlights', $randomSpotlights);
  }

  /**
   * Return three random holidays - note these are cached so they're
   * only random on first request or when the cache expires
   */
  protected function _randomHolidays() {

    $randomHolidays = function() {
      return ClassRegistry::init('HolidayType')->find('random', array(
        'contain' => array(
          'Image',
        ),
        'limit' => 2
      ));
    };

    return $this->cacher('home_holidays', $randomHolidays);
  }

  /**
   * Cache and return the latest blog post
   */
  protected function _blogPost() {
      $blogPost = function() {
          return $this->Page->query("SELECT
             `p`.*,
             CONCAT(`p`.`post_name` , '-150x150.', SUBSTRING_INDEX((SELECT `guid` FROM `wp_posts` WHERE `id` =`m`.`meta_value`), '.', - 1) ) AS `thumbnail`,
             (SELECT `guid` FROM `wp_posts` WHERE `id` = `m`.`meta_value`) AS `full`
          FROM
            `wp_posts` `p`,
            `wp_postmeta` `m`
          WHERE
            `p`.`post_type` = 'post'
            AND `p`.`post_status` = 'publish'
            AND `p`.`id` = `m`.`post_id`
            AND `m`.`meta_key` = '_thumbnail_id'
          ORDER BY
            `p` .`post_date` DESC LIMIT 1;");
      };

      return $this->cacher('home_blog_post', $blogPost);
  }

  /**
   * Returns flat array nav tree of current record
   *
   * @return void
   * <AUTHOR>
  protected function _pathToSelected($nav, $selected = array())
  {
    foreach ($nav as $v) {
      if (!empty($v['children'])) {
        $count = count($selected);

        $selected = $this->_pathToSelected($v['children'], $selected);

        if (count($selected) > $count) {
          array_unshift($selected, array(
            'url'  => $this->_url($v['url']),
            'text' => $v['text'],
          ));
        }
      }

      if (isset($v['selected']) && $v['selected'] === true) {
        $selected[] = array(
          'url'  => $this->_url($v['url']),
          'text' => $v['text']
        );
      }
    }

    return $selected;
  }

  protected function _url($url = array())
  {
    if (!is_array($url)) {
      return $url;
    }

    return Router::url($url);
  }

  protected function _bannerTitle($breadcrumbs)
  {
    $count = count($breadcrumbs);

    if ($count > 1) {
      return $breadcrumbs[$count - 2]['text'];
    }

    return $breadcrumbs[0]['text'];
  }

  function webadmin_edit($id = null) {
    error_log("[Navigation] Starting page edit for ID: " . $id);

    parent::webadmin_edit($id);

    if ($this->data) {
        error_log("[Navigation] Clearing cache due to page update");
        Cache::delete('main_navigation', 'navigation');
    }
  }

    /**
     * Helper to map USA/Canada destinations for mmenu
     */
    function _mapUsaCanadaDestinations($items) {
        $result = array();
        foreach ($items as $dest) {
            $item = array(
                'text' => $dest['Destination']['name'],
                'url' => '/destinations/' . $dest['Destination']['slug']
            );
            if (!empty($dest['Destination']['children'])) {
                $item['has_children'] = true;
                $item['children'] = $this->_mapUsaCanadaDestinations($dest['Destination']['children']);
            }
            $result[] = $item;
        }
        return $result;
    }

    /**
     * Helper to map holiday types for mmenu
     */
    function _mapHolidayTypes($types) {
        $result = array();
        foreach ($types as $type) {
            $result[] = array(
                'text' => $type['HolidayType']['name'],
                'url' => '/holidays/' . $type['HolidayType']['slug']
            );
        }
        return $result;
    }

    /**
     * Helper to map what's hot for mmenu
     */
    function _mapWhatsHot($whatsHot) {
        $result = array();
        foreach ($whatsHot as $spotlight) {
            $result[] = array(
                'text' => $spotlight['Spotlight']['name'],
                'url' => '/spotlights/' . $spotlight['Spotlight']['slug']
            );
        }
        return $result;
    }

    /**
     * Helper to map pages for mmenu
     */
    function _mapPages($pages) {
        $result = array();
        foreach ($pages as $page) {
            $result[] = array(
                'text' => $page['Page']['internal_ref'],
                'url' => '/page/' . $page['Page']['slug']
            );
        }
        return $result;
    }

}

?>
